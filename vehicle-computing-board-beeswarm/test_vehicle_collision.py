#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆碰撞测试脚本
用于验证动态车辆模型的碰撞检测是否正常工作
"""

import rospy
from gazebo_msgs.srv import SetModelState, GetModelState, SpawnModel
from gazebo_msgs.msg import ModelState
from geometry_msgs.msg import Twist
import socket
import time
import tf.transformations as tf_trans

class VehicleCollisionTest:
    """车辆碰撞测试类"""
    
    def __init__(self):
        rospy.init_node('vehicle_collision_test', anonymous=True)
        
        # 等待Gazebo服务
        rospy.wait_for_service('/gazebo/set_model_state')
        rospy.wait_for_service('/gazebo/get_model_state')
        
        self.set_model_state = rospy.ServiceProxy('/gazebo/set_model_state', SetModelState)
        self.get_model_state = rospy.ServiceProxy('/gazebo/get_model_state', GetModelState)
        
        # 获取当前车辆名称
        self.vehicle_name = socket.gethostname()
        
        rospy.loginfo(f"🚗 车辆碰撞测试初始化完成 - 车辆: {self.vehicle_name}")
    
    def set_vehicle_position(self, model_name, x, y, z=0.1, yaw=0.0):
        """设置车辆位置"""
        try:
            model_state = ModelState()
            model_state.model_name = model_name
            
            # 设置位置
            model_state.pose.position.x = x
            model_state.pose.position.y = y
            model_state.pose.position.z = z
            
            # 设置朝向
            quaternion = tf_trans.quaternion_from_euler(0, 0, yaw)
            model_state.pose.orientation.x = quaternion[0]
            model_state.pose.orientation.y = quaternion[1]
            model_state.pose.orientation.z = quaternion[2]
            model_state.pose.orientation.w = quaternion[3]
            
            # 设置速度为0
            model_state.twist.linear.x = 0.0
            model_state.twist.linear.y = 0.0
            model_state.twist.linear.z = 0.0
            model_state.twist.angular.x = 0.0
            model_state.twist.angular.y = 0.0
            model_state.twist.angular.z = 0.0
            
            # 应用到Gazebo
            resp = self.set_model_state(model_state)
            if resp.success:
                rospy.loginfo(f"✅ {model_name} 位置设置成功: ({x:.1f}, {y:.1f})")
                return True
            else:
                rospy.logwarn(f"❌ {model_name} 位置设置失败: {resp.status_message}")
                return False
                
        except Exception as e:
            rospy.logerr(f"❌ 设置 {model_name} 位置时出错: {e}")
            return False
    
    def get_vehicle_position(self, model_name):
        """获取车辆位置"""
        try:
            resp = self.get_model_state(model_name, '')
            if resp.success:
                pos = resp.pose.position
                return pos.x, pos.y, pos.z
            else:
                rospy.logwarn(f"❌ 获取 {model_name} 位置失败")
                return None
        except Exception as e:
            rospy.logerr(f"❌ 获取 {model_name} 位置时出错: {e}")
            return None
    
    def test_collision_detection(self):
        """测试碰撞检测"""
        rospy.loginfo("🧪 开始碰撞检测测试...")
        
        # 测试1: 尝试将两个车辆放在相同位置
        rospy.loginfo("📍 测试1: 尝试将车辆重叠放置")
        
        # 设置当前车辆到原点
        success1 = self.set_vehicle_position(self.vehicle_name, 0.0, 0.0)
        time.sleep(1)
        
        if success1:
            # 获取实际位置
            pos = self.get_vehicle_position(self.vehicle_name)
            if pos:
                rospy.loginfo(f"📊 {self.vehicle_name} 实际位置: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # 测试2: 尝试移动车辆到边界
        rospy.loginfo("📍 测试2: 测试边界碰撞")
        
        # 移动到接近墙壁的位置
        success2 = self.set_vehicle_position(self.vehicle_name, -3.5, 0.0)
        time.sleep(1)
        
        if success2:
            pos = self.get_vehicle_position(self.vehicle_name)
            if pos:
                rospy.loginfo(f"📊 边界测试 - {self.vehicle_name} 位置: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # 测试3: 连续移动测试
        rospy.loginfo("📍 测试3: 连续移动测试")
        
        positions = [
            (1.0, 1.0),
            (2.0, 1.0),
            (2.0, 2.0),
            (1.0, 2.0),
            (0.0, 0.0)
        ]
        
        for i, (x, y) in enumerate(positions):
            rospy.loginfo(f"🚶 移动到位置 {i+1}: ({x}, {y})")
            self.set_vehicle_position(self.vehicle_name, x, y)
            time.sleep(0.5)
            
            pos = self.get_vehicle_position(self.vehicle_name)
            if pos:
                distance_error = ((pos[0] - x)**2 + (pos[1] - y)**2)**0.5
                rospy.loginfo(f"📊 目标: ({x}, {y}), 实际: ({pos[0]:.3f}, {pos[1]:.3f}), 误差: {distance_error:.3f}m")
        
        rospy.loginfo("✅ 碰撞检测测试完成")
    
    def test_vehicle_properties(self):
        """测试车辆属性"""
        rospy.loginfo("🔍 检查车辆模型属性...")
        
        # 这里可以添加更多的车辆属性检查
        # 比如质量、惯性、碰撞几何体等
        
        pos = self.get_vehicle_position(self.vehicle_name)
        if pos:
            rospy.loginfo(f"📊 当前车辆位置: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        else:
            rospy.logwarn(f"❌ 无法获取车辆 {self.vehicle_name} 的位置，可能模型未加载")
    
    def run_all_tests(self):
        """运行所有测试"""
        rospy.loginfo("🚀 开始车辆碰撞测试套件...")
        
        # 等待一下确保Gazebo完全启动
        rospy.loginfo("⏳ 等待Gazebo完全启动...")
        time.sleep(2)
        
        # 检查车辆属性
        self.test_vehicle_properties()
        
        # 测试碰撞检测
        self.test_collision_detection()
        
        rospy.loginfo("🎉 所有测试完成！")
        
        # 提供使用建议
        rospy.loginfo("💡 测试建议:")
        rospy.loginfo("   1. 观察车辆是否能正常移动到指定位置")
        rospy.loginfo("   2. 检查车辆是否会被墙壁阻挡")
        rospy.loginfo("   3. 如果有多个车辆，观察它们是否会相互碰撞")
        rospy.loginfo("   4. 在Gazebo中观察车辆的物理行为")

def main():
    try:
        tester = VehicleCollisionTest()
        tester.run_all_tests()
        
        # 保持节点运行，方便观察
        rospy.loginfo("🔄 测试完成，节点保持运行中...")
        rospy.loginfo("   按 Ctrl+C 退出")
        rospy.spin()
        
    except rospy.ROSInterruptException:
        rospy.loginfo("🛑 测试被用户中断")
    except Exception as e:
        rospy.logerr(f"❌ 测试过程中出错: {e}")

if __name__ == '__main__':
    main()
