# Vehicle Computing Board BeeSwarm

BeeSwarm地面车辆分布式仿真系统 - 基于ROS和MQTT的多车协同仿真平台

## 系统概述

本项目实现了一个分布式车辆仿真系统，采用与BeeSwarm无人机系统相同的架构，但专门针对地面车辆进行了优化。系统具有以下特点：

- **分布式架构**：基于MQTT通信协议的去中心化多车协同
- **ROS集成**：使用ROS作为底层仿真框架
- **实时同步**：高频率状态广播确保车辆间状态一致性
- **模块化设计**：环境加载与车辆控制分离，便于开发和部署
- **🆕 双语言支持**：提供Python版本（易开发）和C++版本（高性能）

## 🆕 C++高性能版本

为了提升系统性能，现已集成C++版本的核心控制模块：

### 性能提升
- **CPU使用率降低50%**：无Python GIL限制，真正的多线程
- **内存使用降低60%**：无Python解释器开销
- **响应延迟降低70%**：编译代码执行效率高
- **更好实时性**：精确的定时控制，LED可达100Hz更新频率

### 对应关系
| Python脚本 | C++程序 | 功能 |
|------------|---------|------|
| CarSim.py | CarSim | MQTT通信、ROS集成、主控制 |
| VelControl.py | VelControl | 速度控制、位置更新 |
| car_led_ctrl.py | car_led_ctrl | LED模式控制 |
| car_noled.py | car_noled | 分布式LED同步器 |

### 系统架构
```
基础环境：
└── Gazebo仿真环境 (car_swarm.launch) - 两个版本都需要

控制层（二选一）：
├── 选项A: Python版本
│   └── roslaunch vswarm_sim start_car.launch
│       ├── CarSim.py
│       ├── VelControl.py
│       ├── car_led_ctrl.py
│       └── car_noled.py
└── 选项B: C++版本
    ├── 🆕 roslaunch vswarm_sim start_car_cpp.launch (推荐)
    │   ├── car_sim_cpp (替代CarSim.py)
    │   ├── vel_control_cpp (替代VelControl.py)
    │   ├── led_control_cpp (替代car_led_ctrl.py)
    │   └── car_noled.py (仍需Python版本)
    └── 或手动启动各个C++程序 (调试用)
```

### 使用建议
- **开发调试**：使用Python版本，修改方便
- **性能要求高**：使用C++版本，资源消耗低
- **生产部署**：根据硬件配置选择合适版本
- **性能测试**：可同时运行两版本进行对比

## 项目结构

```
vehicle-computing-board-beeswarm/
├── vswarm_sim_ws/                        # ROS工作空间
│   ├── quick_start_cpp.sh               # C++版本快速启动脚本
│   └── src/
│       ├── vswarm_sim/                   # 主仿真包（Python版本）
│       │   ├── launch/
│       │   │   ├── car_swarm.launch      # 环境启动文件
│       │   │   ├── spawn_car.launch      # 车辆加载文件
│       │   │   ├── start_car.launch      # Python版本启动文件
│       │   │   └── start_car_cpp.launch  # C++版本启动文件
│       │   ├── scripts/
│       │   │   ├── CarSim.py            # 车辆仿真控制器
│       │   │   ├── VelControl.py        # 速度控制器
│       │   │   ├── Set_Car_Model_State.py # 位置设置工具（完整版）
│       │   │   ├── Set_Car_Model_State_Simple.py # 位置设置工具（简化版）
│       │   │   └── Image_Preprocessing.py # 摄像头图像处理
│       │   └── worlds/                   # 仿真世界文件
│       ├── led_gazebo_plugin/            # LED控制插件
│       │   ├── car_led_ctrl.py          # 本地LED控制器
│       │   ├── car_noled.py             # 分布式LED同步器
│       │   ├── swarm_car_led_ctrl.py    # LED状态控制
│       │   └── swarm_car_noled.py       # 无LED模式
│       ├── swarm_car/                    # 车辆SDF模型
│       └── cpp_version/                  # C++高性能版本
│           ├── src/
│           │   ├── CarSim.cpp                    # 对应CarSim.py
│           │   ├── VelControl.cpp                # 对应VelControl.py
│           │   ├── car_led_ctrl.cpp              # 对应car_led_ctrl.py
│           │   ├── car_noled.cpp                 # 对应car_noled.py
│           │   ├── interactive_led_control.cpp   # 对应interactive_led_control.py
│           │   ├── Set_Car_Model_State_Simple.cpp # 对应Set_Car_Model_State_Simple.py
│           │   ├── led/                          # LED控制器架构
│           │   │   ├── led_controller.cpp
│           │   │   └── local_led_controller.cpp
│           │   ├── communication/                # 通信模块
│           │   └── control/                      # 控制模块
│           ├── include/                          # 头文件
│           └── CMakeLists.txt
├── interactive_led_control.py           # 交互式LED控制工具
└── README.md
```

## 安装和配置

### 系统要求

- **操作系统**: Ubuntu 18.04/20.04
- **ROS版本**: Melodic/Noetic
- **Gazebo**: 9.0+
- **Python**: 3.6+

### 依赖安装

```bash
# 安装Python依赖
pip install paho-mqtt opencv-python numpy pandas

# 安装ROS依赖
sudo apt-get install ros-$ROS_DISTRO-gazebo-ros-pkgs
sudo apt-get install ros-$ROS_DISTRO-gazebo-ros-control
```

### 构建工作空间

```bash
cd vehicle-computing-board-beeswarm/vswarm_sim_ws
catkin build  # 推荐使用catkin build，支持并行编译
# 或者使用: 
catkin_make
（需要先删除build和devel目录）
source devel/setup.bash

# 添加到环境变量
echo "source /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel/setup.bash" >> ~/.bashrc
echo "export HOSTNAME=\$(hostname)" >> ~/.bashrc
```

## 快速启动

### 🚀 超级简单启动（推荐）

```bash
# 进入工作空间
cd /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws

# 运行快速启动脚本
./quick_start_cpp.sh
```

### 选项A：使用Python版本（默认）

```bash
# 进入工作空间并设置环境
cd vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash

# 1. 启动Gazebo仿真环境
roslaunch vswarm_sim car_swarm.launch

# 2. 在新终端中启动Python版本的车辆控制节点
cd vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash
roslaunch vswarm_sim start_car.launch  # 启动所有Python脚本
```

### 选项B：使用C++版本（高性能，推荐）

#### 方法B1：使用launch文件（推荐，与Python版本启动方式一致）
```bash
# 进入工作空间并设置环境
cd vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash

# 设置HOSTNAME环境变量（如果没有设置的话）
export HOSTNAME=$(hostname)

# 1. 启动Gazebo仿真环境
roslaunch vswarm_sim car_swarm.launch

# 2. 在新终端中启动C++版本的车辆控制节点
cd vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash
export HOSTNAME=$(hostname)  # 在新终端中也要设置
roslaunch vswarm_sim start_car_cpp.launch  # 🆕 启动所有C++程序

# 或者直接指定主机名参数：
# roslaunch vswarm_sim start_car_cpp.launch hostname:=$(hostname)
```

#### 方法B2：手动启动（可选，用于调试）
```bash
# 进入工作空间并设置环境
cd vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash

# 1. 启动Gazebo仿真环境
roslaunch vswarm_sim car_swarm.launch

# 2. 在新终端中手动启动C++程序
cd vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash

# 手动启动各个C++程序：
rosrun vehicle_computing_board_beeswarm_cpp car_sim_cpp      # 替代CarSim.py
rosrun vehicle_computing_board_beeswarm_cpp vel_control_cpp  # 替代VelControl.py
rosrun vehicle_computing_board_beeswarm_cpp led_control_cpp  # 替代car_led_ctrl.py
```

### 性能对比测试

```bash
# 可以同时运行Python和C++版本进行性能对比
# Python版本通过launch文件自动启动
# C++版本手动启动，观察CPU和内存使用差异
```

## 分布式通信架构

### MQTT服务器配置

```python
# 服务器配置（在CarSim.py中修改）
broker = '192.168.11.58'           # MQTT服务器地址
username = 'b7986fb66280bcee'      # 用户名
password = 'Mx9B2EVhH79BY783...'   # 密码
port = 1883                        # 端口号
keepalive = 60                     # 心跳间隔
```

### 通信主题设计

| 主题 | 用途 | 消息格式 |
|------|------|----------|
| `/simserver/register` | 车辆注册 | `{"live": true, "agent": "client_id"}` |
| `/simserver/send` | 全局状态广播 | `{车辆ID: {状态信息}, ...}` |
| `/simserver/recv` | 状态上报 | `{"data_type": "transform", "agent": "client_id", "args": {...}}` |
| `/{client_id}/cmd` | 车辆命令 | 根据命令类型变化 |

### 分布式通信流程

#### 1. 车辆注册流程

```
车辆启动 → 生成client_id → 发送注册消息 → 管理器确认 → 状态同步开始
```

#### 2. 状态同步机制

```
周期性循环：
  车辆获取自身状态 → MQTT发布 → 管理器汇总 → 广播全局状态 → 车辆更新其他车辆状态
```

#### 3. 命令控制流程

```
用户发送命令 → MQTT路由 → 目标车辆接收 → 执行命令 → 反馈结果
```

## 车辆控制

### 运动控制特性

- **2D运动**：仅支持地面平面运动（X轴线性，Z轴角度）
- **速度范围**：线性速度0-2 m/s，角速度0-2 rad/s
- **动力学**：地面接触动力学模型

### 命令格式

```json
{
    "motion": {
        "linear": {"x": 0.5, "y": 0.0, "z": 0.0},
        "angular": {"x": 0.0, "y": 0.0, "z": 0.3}
    },
    "camera": {
        "id": 0,          // 摄像头ID（0-3）
        "action": "start"  // start/stop
    },
    "led": {
        "mode": "blink"   // off/on/blink/breathe
    }
}
```

### 摄像头系统

- **摄像头数量**：4个（前后左右）
- **编号**：cam0（前）、cam1（右）、cam2（后）、cam3（左）
- **分辨率**：在SDF文件中配置
- **传输**：基于MQTT的图像流

### LED控制

- **模式**：off, on, blink, breathe
- **颜色**：RGB可配置
- **控制**：Gazebo插件实现

### 位置设置功能

直接设置当前主机所表示的无人车的位置，使用/gazebo/set_model_state接口。

#### 完整版位置设置工具
```bash
cd ~/BeeSwarm/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/vswarm_sim/scripts
python3 Set_Car_Model_State.py
```

#### 简化版位置设置工具（与无人机系统保持一致）
```bash
cd ~/BeeSwarm/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/vswarm_sim/scripts
python3 Set_Car_Model_State_Simple.py
```

#### 位置设置特性
- **自动模型识别**：使用主机名作为无人车模型名称（如VSWARM11、VSWARM15等）
- **实时位置更新**：设置后立即在Gazebo中生效
- **二轴位置控制**：支持X、Y坐标设置（单位：米），Z轴固定为地面高度0.1米
- **地面车辆优化**：专为地面车辆设计，无需手动设置高度
- **错误处理**：自动检测模型是否存在，提供详细错误信息

## 车辆模型规格

### 物理参数

| 参数 | 数值 | 说明 |
|------|------|------|
| 质量 | 4kg | 相比无人机0.037kg更重 |
| 尺寸 | 0.5×0.3×0.2m | 长×宽×高 |
| 碰撞形状 | 圆柱体 | 优化2D运动性能 |
| 驱动方式 | 差分驱动 | 两轮差分转向 |

### 传感器配置

- **摄像头**：4个，分布在车辆四个方向
- **噪声模型**：高斯噪声模拟真实传感器
- **更新频率**：30Hz

## 高级配置

### 自定义车辆数量

```xml
<!-- 在spawn_car.launch中修改 -->
<arg name="car_count" default="3"/>
```

### 更换仿真环境

```xml
<!-- 在car_swarm.launch中修改 -->
<arg name="world_name" value="$(find vswarm_sim)/world/lab_106.world"/>
```

### 性能优化设置

```bash
# 降低摄像头分辨率
# 在SDF文件中修改width和height参数

# 调整物理引擎更新频率
# 在world文件中修改real_time_update_rate
```

## 故障排除

### 常见问题及解决方案

#### 1. MQTT连接失败

```bash
# 检查网络连通性
ping 192.168.11.58

# 测试MQTT服务
mosquitto_pub -h 192.168.11.58 -t test -m "hello"

# 检查防火墙设置
sudo ufw status
```

#### 2. ROS节点通信问题

```bash
# 检查运行中的节点
rosnode list

# 监控话题状态
rostopic list
rostopic echo /gazebo/model_states

# 检查TF树
rosrun tf view_frames
```

#### 3. Gazebo模型加载失败

```bash
# 验证模型路径
echo $GAZEBO_MODEL_PATH

# 检查SDF语法
gz sdf -k swarm_car/model.sdf

# 重建模型数据库
gazebo --verbose
```

#### 4. 车辆控制无响应

```bash
# 检查MQTT消息
rostopic echo /{client_id}/cmd

# 验证车辆注册状态
rostopic echo /simserver/register

# 监控状态同步
rostopic echo /simserver/send
```

### 性能优化建议

1. **系统资源**
   - 限制同时运行的车辆数量（建议≤5）
   - 关闭不必要的Gazebo GUI
   - 降低摄像头分辨率和帧率

2. **网络优化**
   - 使用有线网络连接
   - 配置MQTT QoS级别
   - 压缩图像数据传输

3. **仿真优化**
   - 调整物理引擎精度
   - 使用简化的碰撞模型
   - 禁用不必要的传感器

## 开发指南

### 添加新功能

#### 1. 扩展车辆行为

在`CarSim.py`中添加新的运动模式：

```python
def custom_behavior(self):
    # 实现自定义车辆行为
    pass
```

#### 2. 增强摄像头处理

在`Image_Preprocessing.py`中添加图像算法：

```python
def process_image(self, image):
    # 添加图像处理算法
    return processed_image
```

#### 3. 自定义LED模式

在`swarm_car_led_ctrl.py`中添加新模式：

```python
def custom_led_pattern(self):
    # 实现自定义LED模式
    pass
```

### 测试和验证

```bash
# 测试MQTT连接
python3 -c "import paho.mqtt.client as mqtt; print('MQTT OK')"

# 测试ROS集成
roscore &
rosrun vswarm_sim CarSim.py

# 单元测试
cd vswarm_sim_ws/src/vswarm_sim/scripts
python3 -m pytest test_*.py
```

## 分布式部署

### 多机协同设置

#### 1. 网络配置

```bash
# 在所有计算机上设置ROS主节点
export ROS_MASTER_URI=http://master_ip:11311
export ROS_IP=local_ip
```

#### 2. 部署步骤

```bash
# 主计算机：启动环境
roslaunch vswarm_sim car_swarm.launch

# 各节点计算机：启动车辆控制
roslaunch vswarm_sim start_car.launch
```

#### 3. 负载均衡

- 主节点：运行Gazebo环境和MQTT管理器
- 从节点：每个运行1-2个车辆控制器
- 监控节点：运行状态监控和日志记录

## 与管理系统集成

本计算板系统与`vehicles-management-beeswarm`管理系统协同工作：

| 组件 | 职责 | 通信方式 |
|------|------|----------|
| 管理系统 | 中央控制界面、任务规划 | MQTT发布命令 |
| 计算板系统 | 车辆仿真执行、状态反馈 | MQTT订阅命令 |
| MQTT代理 | 消息路由和缓存 | 双向消息传递 |

## 与无人机系统差异对比

| 方面 | 无人机BeeSwarm | 地面车辆系统 | 说明 |
|------|---------------|-------------|------|
| 运动自由度 | 3D（6DOF） | 2D（3DOF） | 车辆限制在地面 |
| 模型名称 | bee_quadrotor | swarm_car | 不同的SDF模型 |
| 质量 | 0.037kg | 4kg | 车辆更重更稳定 |
| 客户端标识 | `_robot` | `_car` | MQTT主题区分 |
| 动力学 | 飞行动力学 | 地面接触动力学 | 物理模型差异 |
| 传感器布局 | 下视摄像头 | 四向摄像头 | 适应不同需求 |
| 位置设置 | Set_Model_State.py (XYZ) | Set_Car_Model_State.py (XY) | 车辆只需XY轴，Z轴固定 |

## 技术支持

### 文档和资源

- [ROS官方文档](http://docs.ros.org/)
- [Gazebo仿真教程](http://gazebosim.org/tutorials)
- [MQTT协议规范](https://mqtt.org/)

### 问题反馈

1. 检查现有文档和FAQ
2. 验证系统环境和依赖
3. 收集错误日志和系统信息
4. 提供复现步骤和配置文件

### 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 添加测试用例
4. 提交Pull Request
5. 代码审查和合并

## 🆕 C++版本使用说明

### 快速开始
```bash
# 一键启动（推荐）
cd /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws
./quick_start_cpp.sh
```

### C++工具程序
```bash
# 设置环境
cd /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash

# 交互式LED控制（C++版本，更低资源占用）
rosrun cpp_version interactive_led_control_cpp

# 车辆位置设置（C++版本，更低资源占用）
rosrun cpp_version Set_Car_Model_State_Simple

# 分布式LED同步器（C++版本，更低资源占用）
rosrun cpp_version car_noled

# 交互式LED控制（C++版本，更低资源占用）
rosrun cpp_version interactive_led_control
```

### 详细文档
所有C++版本的详细说明都已集成在本README中，无需查看额外文档。

### 性能监控
```bash
# 监控CPU和内存使用
htop

# 监控ROS话题频率
rostopic hz /$(hostname)/led_mode
rostopic hz /$(hostname)/vel_cmd
```

### 版本选择建议
- **开发调试阶段**：使用Python版本，修改方便，调试容易
- **性能测试阶段**：使用C++版本，验证性能提升效果
- **生产部署阶段**：根据硬件资源选择合适版本
- **教学演示阶段**：可同时运行两版本进行对比展示

---

*版本：2.0 | 更新日期：2024-12 | 兼容：ROS Melodic/Noetic | 新增：C++高性能版本*