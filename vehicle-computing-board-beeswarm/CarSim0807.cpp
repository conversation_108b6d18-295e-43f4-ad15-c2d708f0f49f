// C++版本的CarSim.py - 完整对应版本
#include <iostream>
#include <string>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <map>
#include <vector>
#include <algorithm>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <fstream>
#include <mutex>

#include <ros/ros.h>
#include <std_msgs/Int32.h>
#include <std_msgs/Float32MultiArray.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/Pose.h>
#include <gazebo_msgs/SpawnModel.h>
#include <gazebo_msgs/ModelStates.h>
#include <gazebo_msgs/ModelState.h>

#include <mosquitto.h>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

// 分布式LED控制器类
class DistributedLEDController {
public:
    DistributedLEDController(const std::string& client_id, struct mosquitto* mosq) 
        : client_id_(client_id), mosq_(mosq), led_mode_(0), last_broadcast_time_(0.0), broadcast_interval_(1.0) {
        
        is_master_ = (client_id == "VCAR01_car");  // 指定主控车辆
        master_id_ = "VCAR01_car";
        
        // 创建节点特定的ROS发布者
        try {
            std::string node_name = client_id;
            // 移除 "_car" 后缀
            if (node_name.length() > 4 && node_name.substr(node_name.length() - 4) == "_car") {
                node_name = node_name.substr(0, node_name.length() - 4);
            }
            
            ros::NodeHandle nh;
            std::string led_topic = "/" + node_name + "/led_mode";
            led_mode_pub_ = nh.advertise<std_msgs::Int32>(led_topic, 10);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 等待发布者初始化
            ROS_INFO("DistributedLEDController created publisher: %s", led_topic.c_str());
        } catch (...) {
            ROS_ERROR("Failed to create LED mode publisher");
        }
        
        ROS_INFO("DistributedLEDController initialized for %s, Master: %s", 
                 client_id_.c_str(), is_master_ ? "true" : "false");
    }
    
    void setLedMode(int mode) {
        if (is_master_) {
            // 主控节点：广播给所有从节点
            led_mode_ = mode;
            broadcastLedCommand(mode);
            localLedControl(mode);
        } else {
            // 从节点：只能本地设置（通常由主控节点触发）
            led_mode_ = mode;
            localLedControl(mode);
        }
    }
    
    void broadcastLedCommand(int mode) {
        if (!is_master_) {
            ROS_WARN("Non-master node %s attempted to broadcast", client_id_.c_str());
            return;
        }
        
        double current_time = getCurrentTime();
        // 避免频繁广播
        if (current_time - last_broadcast_time_ < broadcast_interval_) {
            return;
        }
        
        try {
            json led_cmd = {
                {"cmd_type", "led_mode_master"},
                {"led_mode", mode},
                {"master_id", client_id_},
                {"timestamp", current_time}
            };
            
            // 广播给所有节点
            std::string payload = led_cmd.dump();
            mosquitto_publish(mosq_, nullptr, "/Broadcast/led_master_cmd", 
                            payload.length(), payload.c_str(), 0, false);
            
            last_broadcast_time_ = current_time;
            ROS_INFO("Master %s broadcasted LED mode: %d", client_id_.c_str(), mode);
            
        } catch (const std::exception& e) {
            ROS_ERROR("Error broadcasting LED command: %s", e.what());
        }
    }
    
    void handleMasterCommand(const json& cmd_msg) {
        try {
            if (is_master_) {
                return; // 主控节点忽略自己的广播
            }
            
            if (cmd_msg["cmd_type"] == "led_mode_master") {
                std::string master_id = cmd_msg["master_id"];
                int led_mode = cmd_msg["led_mode"];
                
                // 验证主控节点身份
                if (master_id == master_id_) {
                    ROS_INFO("Slave %s received master command: mode %d", 
                             client_id_.c_str(), led_mode);
                    setLedMode(led_mode);
                } else {
                    ROS_WARN("Received command from unauthorized master: %s", master_id.c_str());
                }
            }
            
        } catch (const std::exception& e) {
            ROS_ERROR("Error handling master command: %s", e.what());
        }
    }
    
    void localLedControl(int mode) {
        try {
            std_msgs::Int32 msg;
            msg.data = mode;
            led_mode_pub_.publish(msg);
            ROS_INFO("Published LED mode %d to local ROS", mode);
        } catch (const std::exception& e) {
            ROS_ERROR("Error in local LED control: %s", e.what());
        }
    }
    
    json getStatus() const {
        return {
            {"client_id", client_id_},
            {"is_master", is_master_},
            {"led_mode", led_mode_},
            {"master_id", master_id_}
        };
    }
    
    bool isMaster() const { return is_master_; }
    int getLedMode() const { return led_mode_; }
    
private:
    std::string client_id_;
    struct mosquitto* mosq_;
    bool is_master_;
    std::string master_id_;
    int led_mode_;
    double last_broadcast_time_;
    double broadcast_interval_;
    ros::Publisher led_mode_pub_;
    
    double getCurrentTime() {
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration<double>(duration).count();
    }
};

// MQTT客户端类
class MQTTClientThread {
public:
    MQTTClientThread(const std::string& broker, int port, int keepalive, const std::string& client_id)
        : broker_(broker), port_(port), keepalive_(keepalive), client_id_(client_id) {
        
        recv_topic_ = "/simserver/recv";
        send_topic_ = "/simserver/send";
        reg_topic_ = "/simserver/register";
        
        mosq_ = connectMQTT();
        if (mosq_) {
            // 初始化分布式LED控制器
            led_controller_ = std::make_unique<DistributedLEDController>(client_id_, mosq_);
        }
    }
    
    ~MQTTClientThread() {
        if (mosq_) {
            mosquitto_disconnect(mosq_);
            mosquitto_destroy(mosq_);
            mosquitto_lib_cleanup();
        }
    }
    
    void publish(const std::string& topic, const std::string& msg) {
        if (!mosq_) return;
        
        int result = mosquitto_publish(mosq_, nullptr, topic.c_str(), 
                                     msg.length(), msg.c_str(), 0, false);
        if (result != 0) {
            ROS_ERROR("Failed to send message to topic %s", topic.c_str());
        }
    }
    
    void setSubscribe() {
        if (!mosq_) return;
        
        mosquitto_subscribe(mosq_, nullptr, "/simserver/send", 0);
        ROS_INFO("Subscribed to /simserver/send topic");
        
        // 订阅LED控制相关话题
        std::string single_cmd_topic = "/" + client_id_ + "/cmd";
        mosquitto_subscribe(mosq_, nullptr, single_cmd_topic.c_str(), 0);
        mosquitto_subscribe(mosq_, nullptr, "/Broadcast/cmd", 0);
        mosquitto_subscribe(mosq_, nullptr, "/Broadcast/led_master_cmd", 0);
        
        ROS_INFO("Subscribed to LED control topics for %s", client_id_.c_str());
    }
    
    void run() {
        if (!mosq_) return;

        try {
            int loop_result = mosquitto_loop_forever(mosq_, -1, 1);
            if (loop_result != MOSQ_ERR_SUCCESS) {
                ROS_ERROR("MQTT loop failed: %d", loop_result);
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Exception in MQTT loop: %s", e.what());
        } catch (...) {
            ROS_ERROR("Unknown exception in MQTT loop");
        }
    }
    
    json agent_dict_;
    std::mutex agent_dict_mutex_;  // 添加互斥锁保护agent_dict_
    std::unique_ptr<DistributedLEDController> led_controller_;

    // 线程安全的agent_dict访问方法
    json getAgentDictCopy() {
        std::lock_guard<std::mutex> lock(agent_dict_mutex_);
        return agent_dict_;  // 返回副本
    }

    bool containsAgent(const std::string& agent_id) {
        std::lock_guard<std::mutex> lock(agent_dict_mutex_);
        return agent_dict_.contains(agent_id);
    }

    json getAgentData(const std::string& agent_id) {
        std::lock_guard<std::mutex> lock(agent_dict_mutex_);
        if (agent_dict_.contains(agent_id)) {
            return agent_dict_[agent_id];
        }
        return json();  // 返回空JSON
    }

    // 公共访问的成员变量
    std::string client_id_;
    std::string recv_topic_;
    std::string send_topic_;
    std::string reg_topic_;

private:
    std::string broker_;
    int port_;
    int keepalive_;
    struct mosquitto* mosq_;
    
    struct mosquitto* connectMQTT() {
        static bool lib_initialized = false;
        if (!lib_initialized) {
            mosquitto_lib_init();
            lib_initialized = true;
        }

        struct mosquitto* mosq = mosquitto_new(client_id_.c_str(), true, this);
        if (!mosq) {
            ROS_ERROR("Failed to create MQTT client");
            return nullptr;
        }

        mosquitto_connect_callback_set(mosq, onConnect);
        mosquitto_message_callback_set(mosq, onMessage);

        int result = mosquitto_connect(mosq, broker_.c_str(), port_, keepalive_);
        if (result != MOSQ_ERR_SUCCESS) {
            ROS_ERROR("Failed to connect to MQTT broker: %d", result);
            mosquitto_destroy(mosq);
            return nullptr;
        }

        ROS_INFO("Connected to MQTT OK!");
        return mosq;
    }
    
    static void onConnect(struct mosquitto* mosq, void* userdata, int result) {
        MQTTClientThread* self = static_cast<MQTTClientThread*>(userdata);
        
        if (result == 0) {
            ROS_INFO("Connected to MQTT OK!");
            // 连接成功后立即订阅
            mosquitto_subscribe(mosq, nullptr, "/simserver/send", 0);
            
            std::string single_cmd_topic = "/" + self->client_id_ + "/cmd";
            mosquitto_subscribe(mosq, nullptr, single_cmd_topic.c_str(), 0);
            mosquitto_subscribe(mosq, nullptr, "/Broadcast/cmd", 0);
            mosquitto_subscribe(mosq, nullptr, "/Broadcast/led_master_cmd", 0);
            
            ROS_INFO("Auto-subscribed to all control topics on connect");
        } else {
            ROS_ERROR("Failed to connect, return code %d", result);
        }
    }
    
    static void onMessage(struct mosquitto* mosq, void* userdata,
                         const struct mosquitto_message* message) {
        // 增强安全检查
        if (!mosq || !userdata || !message || !message->topic || !message->payload || message->payloadlen <= 0) {
            ROS_WARN("Received invalid MQTT message or null pointers");
            return;
        }

        MQTTClientThread* self = static_cast<MQTTClientThread*>(userdata);
        if (!self) {
            ROS_ERROR("Invalid userdata in MQTT callback");
            return;
        }

        std::string topic(message->topic);

        // 更安全的payload处理
        std::string payload;
        try {
            payload.assign(static_cast<const char*>(message->payload), message->payloadlen);

            // 验证payload是有效的UTF-8字符串
            if (payload.empty()) {
                ROS_WARN("Received empty payload for topic: %s", topic.c_str());
                return;
            }

            // 检查是否包含null字符（可能表示数据损坏）
            if (payload.find('\0') != std::string::npos) {
                ROS_WARN("Payload contains null characters, possible data corruption");
                // 截断到第一个null字符
                payload = payload.substr(0, payload.find('\0'));
            }

        } catch (const std::exception& e) {
            ROS_ERROR("Error processing MQTT payload: %s", e.what());
            return;
        }

        try {
            if (topic == "/simserver/send") {
                // 添加调试信息：打印原始payload
                ROS_DEBUG("Raw MQTT payload length: %zu", payload.length());
                ROS_DEBUG("Raw MQTT payload (first 200 chars): %s", payload.substr(0, 200).c_str());

                json rev_msg;
                try {
                    rev_msg = json::parse(payload);
                } catch (const json::parse_error& e) {
                    ROS_ERROR("JSON parse error: %s", e.what());
                    ROS_ERROR("Problematic payload: %s", payload.c_str());
                    return;
                }

                // 调试：打印解析后的JSON结构
                ROS_DEBUG("Parsed JSON type: %s", rev_msg.type_name());
                ROS_DEBUG("Parsed JSON size: %zu", rev_msg.size());

                // 验证JSON是对象类型
                if (!rev_msg.is_object()) {
                    ROS_ERROR("Received non-object JSON: %s", rev_msg.dump().c_str());
                    return;
                }

                // 验证和清理agent_dict数据（简化版本，类似Python）
                json cleaned_agent_dict;
                for (auto it = rev_msg.begin(); it != rev_msg.end(); ++it) {
                    const std::string& key = it.key();
                    const json& value = it.value();

                    // 调试：打印每个键值对
                    ROS_DEBUG("Processing key: '%s', value type: %s", key.c_str(), value.type_name());

                    // 跳过空键或null值（降低日志级别）
                    if (key.empty() || value.is_null()) {
                        ROS_DEBUG("Skipping null agent value for key: %s", key.c_str());
                        continue;
                    }

                    // 跳过看起来像属性名的键（这些不应该是agent名称）
                    if (key == "pos_x" || key == "pos_y" || key == "pos_z" ||
                        key == "q_x" || key == "q_y" || key == "q_z" || key == "q_w" ||
                        key == "led_mode") {
                        ROS_WARN("Skipping property key in agent_dict: %s", key.c_str());
                        continue;
                    }

                    // 验证value是一个有效的agent对象
                    if (value.is_object()) {
                        // 进一步验证agent对象包含必要字段
                        if (value.contains("pos_x") && value.contains("pos_y") && value.contains("pos_z")) {
                            cleaned_agent_dict[key] = value;
                            ROS_DEBUG("Added valid agent: %s", key.c_str());
                        } else {
                            ROS_WARN("Agent %s missing required position fields", key.c_str());
                        }
                    } else {
                        ROS_WARN("Skipping invalid agent data for key: %s (type: %s)", key.c_str(), value.type_name());
                    }
                }

                // 线程安全地更新agent_dict_
                {
                    std::lock_guard<std::mutex> lock(self->agent_dict_mutex_);

                    // 只有当清理后的agent_dict发生变化时才更新
                    if (cleaned_agent_dict != self->agent_dict_) {
                        self->agent_dict_ = cleaned_agent_dict;
                        ROS_INFO("Agent dict updated with %zu agents", self->agent_dict_.size());
                        // 调试：打印agent_dict的键
                        for (auto it = self->agent_dict_.begin(); it != self->agent_dict_.end(); ++it) {
                            ROS_INFO("Agent key: '%s', value type: %s",
                                    it.key().c_str(),
                                    it.value().is_null() ? "null" : "object");
                        }
                    } else {
                        self->agent_dict_ = cleaned_agent_dict; // 静默更新
                    }
                }
            }
            // 处理单播LED控制命令
            else if (topic == "/" + self->client_id_ + "/cmd") {
                json cmd_msg = json::parse(payload);
                if (cmd_msg["cmd_type"] == "ledup" || cmd_msg["cmd_type"] == "leddown") {
                    ROS_INFO("Received LED command");
                    self->handleLedCommand(cmd_msg);
                } else if (cmd_msg["cmd_type"] == "motion") {
                    ROS_INFO("Received motion command");
                    self->handleMotionCommand(cmd_msg);
                }
            }
            // 处理广播LED控制命令
            else if (topic == "/Broadcast/cmd") {
                json cmd_msg = json::parse(payload);
                if (cmd_msg["cmd_type"] == "ledup" || cmd_msg["cmd_type"] == "leddown") {
                    ROS_INFO("Received broadcast LED command");
                    self->handleLedCommand(cmd_msg);
                }
            }
            // 处理主控LED命令
            else if (topic == "/Broadcast/led_master_cmd") {
                json cmd_msg = json::parse(payload);
                if (cmd_msg["cmd_type"] == "led_mode_master") {
                    ROS_INFO("Received master LED command");
                    self->handleMasterLedCommand(cmd_msg);
                }
            }
            
        } catch (const std::exception& e) {
            ROS_ERROR("Error processing MQTT message: %s", e.what());
        }
    }
    
    void handleLedCommand(const json& cmd_msg) {
        try {
            // 将MQTT LED命令转换为ROS消息
            if (cmd_msg["cmd_type"] == "ledup") {
                publishLedCommand("upper", cmd_msg["args"]);
            } else if (cmd_msg["cmd_type"] == "leddown") {
                publishLedCommand("lower", cmd_msg["args"]);
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Error handling LED command: %s", e.what());
        }
    }
    
    void handleMotionCommand(const json& cmd_msg) {
        try {
            // 运动控制逻辑
            double linear_x = cmd_msg["args"]["0"];
            double angular_z = cmd_msg["args"]["3"];
            ROS_INFO("Motion command: linear_x=%f, angular_z=%f", linear_x, angular_z);
        } catch (const std::exception& e) {
            ROS_ERROR("Error handling motion command: %s", e.what());
        }
    }
    
    void handleMasterLedCommand(const json& cmd_msg) {
        try {
            if (led_controller_) {
                led_controller_->handleMasterCommand(cmd_msg);
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Error handling master LED command: %s", e.what());
        }
    }
    
    void publishLedCommand(const std::string& position, const json& args) {
        try {
            int color_value = args["0"];
            int led_mode = colorToLedMode(color_value);
            publishLedMode(led_mode);
            
            ROS_INFO("Published LED command: %s - color 0x%06X -> mode %d", 
                     position.c_str(), color_value, led_mode);
                     
        } catch (const std::exception& e) {
            ROS_ERROR("Error publishing LED command: %s", e.what());
        }
    }
    
    int colorToLedMode(int color_value) {
        // 根据颜色值确定LED模式
        switch (color_value) {
            case 0xFF0000: return 1; // 红色
            case 0x00FF00: return 2; // 绿色
            case 0x0000FF: return 3; // 蓝色
            case 0xFFFF00: return 4; // 黄色
            case 0xFF00FF: return 5; // 紫色
            default: return 0;       // 默认模式
        }
    }
    
    void publishLedMode(int mode) {
        try {
            if (led_controller_) {
                led_controller_->setLedMode(mode);
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Error publishing LED mode: %s", e.what());
        }
    }
};

// Gazebo节点线程类
class GazeboNodeThread {
public:
    GazeboNodeThread(std::shared_ptr<MQTTClientThread> mqtt_client, const std::string& client_id)
        : mqtt_client_(mqtt_client), client_id_(client_id), px_(0.0), py_(0.0), pz_(0.0),
          led_mode_(0), last_led_mode_(0), led_sync_timestamp_(0.0) {
        
        try {
            // 初始化ROS节点
            ros::NodeHandle nh;
            
            set_model_states_ = nh.advertise<gazebo_msgs::ModelState>("/gazebo/set_model_state", 10);
            ROS_INFO("Connected to ROS OK!");
            
            // 主从控制设置
            is_master_ = (mqtt_client_->client_id_ == "VCAR01_car");
            master_id_ = "VCAR01";  // 主控车辆的模型名称（不带_car后缀）
            
            // 创建节点特定的LED相关发布者
            std::string node_name = mqtt_client_->client_id_;
            if (node_name.length() > 4 && node_name.substr(node_name.length() - 4) == "_car") {
                node_name = node_name.substr(0, node_name.length() - 4);
            }
            
            std::string led_topic = "/" + node_name + "/led_mode";
            model_led_publishers_ = nh.advertise<std_msgs::Int32>(led_topic, 10);
            ROS_INFO("Created model LED publisher: %s", led_topic.c_str());
            
            ROS_INFO("Vehicle %s initialized. Master mode: %s", 
                     mqtt_client_->client_id_.c_str(), is_master_ ? "true" : "false");
                     
        } catch (const std::exception& e) {
            ROS_ERROR("Connected to ROS Failed: %s", e.what());
        }
    }
    
    void ledCallback(const std_msgs::Int32::ConstPtr& msg) {
        int old_led_mode = led_mode_;
        led_mode_ = msg->data;
        
        // 增强LED状态变化日志
        if (old_led_mode != led_mode_) {
            ROS_INFO("🔄 %s LED mode changed: %d -> %d", 
                     mqtt_client_->client_id_.c_str(), old_led_mode, led_mode_);
            if (led_mode_ == 0) {
                ROS_INFO("🔄 %s LED reset to default state", mqtt_client_->client_id_.c_str());
            }
        }
        
        // 立即通过MQTT发送LED状态变化（所有节点都发送）
        if (old_led_mode != led_mode_) {
            sendLedStatusImmediately();
        }
        
        // 如果是主控节点且LED模式发生变化，广播给其他节点
        if (is_master_ && old_led_mode != led_mode_) {
            broadcastMasterLedCommand(led_mode_);
        }
    }
    
    void sendLedStatusImmediately() {
        try {
            json rev_msg = {
                {"data_type", "transform"},
                {"agent", client_id_},
                {"args", {
                    {"pos_x", px_},
                    {"pos_y", py_},
                    {"pos_z", pz_},
                    {"led_mode", led_mode_}
                }}
            };

            mqtt_client_->publish(mqtt_client_->recv_topic_, rev_msg.dump());
            ROS_INFO("Immediately sent LED status for %s: mode %d",
                     client_id_.c_str(), led_mode_);
                     
        } catch (const std::exception& e) {
            ROS_ERROR("Error sending immediate LED status: %s", e.what());
        }
    }
    
    void publishOtherNodeLedStatus(const std::string& node_name, int led_mode) {
        try {
            // 为每个其他节点创建LED模式发布者
            if (other_node_led_mode_publishers_.find(node_name) == other_node_led_mode_publishers_.end()) {
                ros::NodeHandle nh;
                std::string topic_name = "/other_node_led_mode/" + node_name;
                other_node_led_mode_publishers_[node_name] = nh.advertise<std_msgs::Int32>(topic_name, 10);
                std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 等待发布者初始化
                ROS_INFO("Created other node LED mode publisher: %s", topic_name.c_str());
            }
            
            // 发布其他节点的LED模式到本地LED控制器
            std_msgs::Int32 msg;
            msg.data = led_mode;
            other_node_led_mode_publishers_[node_name].publish(msg);
            
            ROS_INFO("Notified local LED controller: %s LED mode %d", node_name.c_str(), led_mode);
            
        } catch (const std::exception& e) {
            ROS_ERROR("Error notifying LED controller for %s: %s", node_name.c_str(), e.what());
        }
    }
    
    void broadcastMasterLedCommand(int led_mode) {
        try {
            json master_cmd = {
                {"cmd_type", "led_mode_master"},
                {"led_mode", led_mode},
                {"master_id", mqtt_client_->client_id_},
                {"timestamp", getCurrentTime()}
            };
            
            // 广播给所有节点
            mqtt_client_->publish("/Broadcast/led_master_cmd", master_cmd.dump());
            ROS_INFO("Master %s broadcasted LED mode: %d", 
                     mqtt_client_->client_id_.c_str(), led_mode);
                     
        } catch (const std::exception& e) {
            ROS_ERROR("Error broadcasting master LED command: %s", e.what());
        }
    }
    
    void getModelStates(const gazebo_msgs::ModelStates::ConstPtr& msg) {
        try {
            // 简单直接的实现，类似Python版本
            auto it = std::find(msg->name.begin(), msg->name.end(), model_name_);
            if (it != msg->name.end()) {
                int index = std::distance(msg->name.begin(), it);
                const auto& carPos = msg->pose[index];

                px_ = carPos.position.x;
                py_ = carPos.position.y;
                pz_ = carPos.position.z;
            }

            // 遍历所有模型，更新其他车辆状态
            for (size_t i = 0; i < msg->name.size(); ++i) {
                const std::string& model_name = msg->name[i];
                if (std::find(model_list_.begin(), model_list_.end(), model_name) != model_list_.end()) {
                    const auto& carPos = msg->pose[i];

                    // 更新模型状态
                    model_states_dict_[model_name] = {
                        {"pos_x", carPos.position.x},
                        {"pos_y", carPos.position.y},
                        {"pos_z", carPos.position.z}
                    };
                }
            }

        } catch (const std::exception& e) {
            ROS_ERROR("Error processing model states: %s", e.what());
        }
    }
    
    std::string simInit(const json& pos) {
        // 基本安全检查
        if (pos.is_null() || !pos.is_object()) {
            ROS_ERROR("simInit: Invalid position data");
            return "";
        }

        // 获取 SDF 文件路径
        ros::NodeHandle nh;
        std::string swarm_car;
        if (!nh.getParam("/swarm_car", swarm_car)) {
            ROS_ERROR("Failed to get /swarm_car parameter");
            return "";
        }
        
        // 读取 SDF 文件内容
        std::ifstream sdf_file(swarm_car);
        if (!sdf_file.is_open()) {
            ROS_ERROR("Failed to open SDF file: %s", swarm_car.c_str());
            return "";
        }
        
        std::stringstream sdf_stream;
        sdf_stream << sdf_file.rdbuf();
        std::string sdf_content = sdf_stream.str();
        sdf_file.close();
        
        // 模型名称和命名空间（与Python版本保持一致）
        std::string model_name = client_id_;  // 直接使用基础client_id
        std::string model_namespace = "";
        
        // 初始姿态（安全访问JSON字段）
        geometry_msgs::Pose initial_pose;
        if (!pos.contains("pos_x") || !pos.contains("pos_y") || !pos.contains("pos_z")) {
            ROS_WARN("simInit: Missing position fields for %s, using defaults", model_name.c_str());
        }
        // 更安全的字段访问
        if (pos.contains("pos_x") && !pos["pos_x"].is_null()) {
            initial_pose.position.x = pos["pos_x"].get<double>();
        } else {
            initial_pose.position.x = 0.0;
        }

        if (pos.contains("pos_y") && !pos["pos_y"].is_null()) {
            initial_pose.position.y = pos["pos_y"].get<double>();
        } else {
            initial_pose.position.y = 0.0;
        }

        if (pos.contains("pos_z") && !pos["pos_z"].is_null()) {
            initial_pose.position.z = pos["pos_z"].get<double>();
        } else {
            initial_pose.position.z = 0.0;
        }
        initial_pose.orientation.w = 1.0;
        
        // 使用 gazebo/spawn_sdf_model 服务来加载 SDF 模型
        ros::service::waitForService("/gazebo/spawn_sdf_model");
        try {
            ros::ServiceClient spawn_sdf_model = nh.serviceClient<gazebo_msgs::SpawnModel>("/gazebo/spawn_sdf_model");
            gazebo_msgs::SpawnModel srv;
            srv.request.model_name = model_name;
            srv.request.model_xml = sdf_content;
            srv.request.robot_namespace = model_namespace;
            srv.request.initial_pose = initial_pose;
            srv.request.reference_frame = "world";
            
            if (spawn_sdf_model.call(srv)) {
                if (srv.response.success) {
                    ROS_INFO("Spawned %s successfully!", model_name.c_str());
                } else {
                    ROS_ERROR("Spawn failed: %s", srv.response.status_message.c_str());
                }
            } else {
                ROS_ERROR("Failed to call spawn service");
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Spawn service failed: %s", e.what());
        }
        
        return model_name;
    }
    
    std::string addAgent(const json& pos, const std::string& id) {
        // 验证输入参数
        if (id.empty()) {
            ROS_ERROR("addAgent: Empty agent ID provided");
            return "";
        }

        if (pos.is_null() || !pos.is_object()) {
            ROS_ERROR("addAgent: Invalid position data for agent %s", id.c_str());
            return "";
        }

        // 获取/swarm_car_no_pub参数的值
        ros::NodeHandle nh;
        std::string swarm_car_no_pub;
        if (!nh.getParam("/swarm_car_no_pub", swarm_car_no_pub)) {
            ROS_ERROR("Failed to get /swarm_car_no_pub parameter");
            return "";
        }

        // 读取 SDF 文件内容
        std::ifstream sdf_file(swarm_car_no_pub);
        if (!sdf_file.is_open()) {
            ROS_ERROR("Failed to open SDF file: %s", swarm_car_no_pub.c_str());
            return "";
        }

        std::stringstream sdf_stream;
        sdf_stream << sdf_file.rdbuf();
        std::string sdf_content = sdf_stream.str();
        sdf_file.close();

        // 模型名称和命名空间
        std::string model_name = id;
        std::string model_namespace = id;

        // 初始姿态（安全访问JSON字段）
        geometry_msgs::Pose initial_pose;
        if (!pos.contains("pos_x") || !pos.contains("pos_y") || !pos.contains("pos_z")) {
            ROS_WARN("addAgent: Missing position fields for %s, using defaults", model_name.c_str());
        }
        // 更安全的字段访问
        if (pos.contains("pos_x") && !pos["pos_x"].is_null()) {
            initial_pose.position.x = pos["pos_x"].get<double>();
        } else {
            initial_pose.position.x = 0.0;
        }

        if (pos.contains("pos_y") && !pos["pos_y"].is_null()) {
            initial_pose.position.y = pos["pos_y"].get<double>();
        } else {
            initial_pose.position.y = 0.0;
        }

        if (pos.contains("pos_z") && !pos["pos_z"].is_null()) {
            initial_pose.position.z = pos["pos_z"].get<double>();
        } else {
            initial_pose.position.z = 0.0;
        }
        initial_pose.orientation.w = 1.0;
        
        // 使用 gazebo/spawn_sdf_model 服务来加载 SDF 模型
        ros::service::waitForService("/gazebo/spawn_sdf_model");
        try {
            ros::ServiceClient spawn_sdf_model = nh.serviceClient<gazebo_msgs::SpawnModel>("/gazebo/spawn_sdf_model");
            gazebo_msgs::SpawnModel srv;
            srv.request.model_name = model_name;
            srv.request.model_xml = sdf_content;
            srv.request.robot_namespace = model_namespace;
            srv.request.initial_pose = initial_pose;
            srv.request.reference_frame = "world";
            
            if (spawn_sdf_model.call(srv)) {
                if (srv.response.success) {
                    ROS_INFO("Spawned %s successfully!", model_name.c_str());
                } else {
                    ROS_ERROR("Spawn failed: %s", srv.response.status_message.c_str());
                }
            } else {
                ROS_ERROR("Failed to call spawn service");
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Spawn service failed: %s", e.what());
        }
        
        return model_name;
    }
    
    void run() {
        // 初始化注册（与Python版本保持一致）
        json reg_msg = {
            {"live", true},
            {"agent", client_id_}  // 直接使用基础client_id
        };
        
        bool reg_state = false;
        mqtt_client_->publish(mqtt_client_->reg_topic_, reg_msg.dump());
        
        ros::NodeHandle nh;
        model_led_publishers_ = nh.advertise<std_msgs::Int32>("/led_mode", 10);
        
        int ros_hz = 10;  // 降低到10Hz，减少竞争
        ros::Rate rate(ros_hz);
        
        while (ros::ok()) {
            try {
                // 检查注册状态（与Python版本保持一致）
                if (!reg_state) {
                    // 创建副本以避免多线程竞争
                    json agent_dict_copy = mqtt_client_->agent_dict_;
                    if (agent_dict_copy.contains(client_id_)) {
                        reg_state = true;
                        const json& agent_data = agent_dict_copy[client_id_];

                        if (!agent_data.is_null() && agent_data.is_object()) {
                            model_name_ = simInit(agent_data);
                            ROS_INFO("Registration successful for %s", client_id_.c_str());

                            // 注册成功后的一次性初始化
                            model_states_sub_ = nh.subscribe("/gazebo/model_states", 10,
                                                           &GazeboNodeThread::getModelStates, this);

                            // 订阅本地节点特定的LED模式话题
                            std::string node_name = mqtt_client_->client_id_;
                            if (node_name.length() > 4 && node_name.substr(node_name.length() - 4) == "_car") {
                                node_name = node_name.substr(0, node_name.length() - 4);
                            }

                            std::string local_led_topic = "/" + node_name + "/led_mode";
                            led_mode_sub_ = nh.subscribe(local_led_topic, 10, &GazeboNodeThread::ledCallback, this);
                            ROS_INFO("Subscribed to local LED topic: %s", local_led_topic.c_str());

                        } else {
                            ROS_ERROR("Agent data is invalid for %s", client_id_.c_str());
                        }
                    }
                }
                
                if (reg_state) {
                    // 添加新的代理（与Python版本保持一致）
                    // 创建副本以避免多线程竞争
                    json agent_dict_copy = mqtt_client_->agent_dict_;
                    for (auto it = agent_dict_copy.begin(); it != agent_dict_copy.end(); ++it) {
                        const std::string& each = it.key();
                        const json& agent_data = it.value();

                        // 严格验证agent名称
                        if (each.empty()) {
                            ROS_WARN("Skipping empty agent key in agent_dict");
                            continue;
                        }

                        // 检查值是否为null或不是对象
                        if (agent_data.is_null() || !agent_data.is_object()) {
                            ROS_WARN("Skipping null/invalid agent value for key: %s", each.c_str());
                            continue;
                        }

                        // 关键修复：跳过看起来像属性名的键（这些不应该是agent名称）
                        if (each == "pos_x" || each == "pos_y" || each == "pos_z" ||
                            each == "q_x" || each == "q_y" || each == "q_z" || each == "q_w" ||
                            each == "led_mode") {
                            ROS_WARN("Skipping property key masquerading as agent: %s", each.c_str());
                            continue;
                        }

                        // 验证agent数据包含必要字段
                        if (!agent_data.contains("pos_x") || !agent_data.contains("pos_y") || !agent_data.contains("pos_z")) {
                            ROS_WARN("Agent %s missing required position fields, skipping", each.c_str());
                            continue;
                        }

                        if (std::find(model_list_.begin(), model_list_.end(), each) == model_list_.end()
                            && each != client_id_) {
                            ROS_INFO("Adding new agent: %s to Gazebo", each.c_str());
                            model_list_.push_back(each);
                            try {
                                addAgent(agent_data, each);
                            } catch (const std::exception& e) {
                                ROS_ERROR("Failed to add agent %s: %s", each.c_str(), e.what());
                                // 从model_list中移除失败的agent
                                model_list_.erase(std::remove(model_list_.begin(), model_list_.end(), each), model_list_.end());
                            }
                        }
                    }
                    
                    try {
                        // 发送位置和LED状态信息（与Python版本保持一致）
                        json rev_msg = {
                            {"data_type", "transform"},
                            {"agent", client_id_},
                            {"args", {
                                {"pos_x", px_},
                                {"pos_y", py_},
                                {"pos_z", pz_},
                                {"led_mode", led_mode_}
                            }}
                        };
                        mqtt_client_->publish(mqtt_client_->recv_topic_, rev_msg.dump());

                    } catch (...) {
                        // 忽略错误
                    }
                    
                    try {
                        // 主控节点LED模式发布逻辑
                        if (is_master_ && (led_mode_ != last_led_mode_)) {
                            std_msgs::Int32 msg;
                            msg.data = led_mode_;
                            model_led_publishers_.publish(msg);
                            last_led_mode_ = led_mode_;
                            ROS_INFO("Master %s Published led_mode: %d", 
                                     mqtt_client_->client_id_.c_str(), msg.data);
                        }
                        
                        // 在主循环中处理和发布模型状态
                        for (const std::string& each : model_list_) {
                            if (mqtt_client_->containsAgent(each)) {
                                try {
                                    json agent_data = mqtt_client_->getAgentData(each);
                                    if (agent_data.is_null()) continue;

                                    // 检查agent数据是否为null或缺少必要字段
                                    if (agent_data.is_null()) {
                                        ROS_WARN("Agent %s has null data, skipping", each.c_str());
                                        continue;
                                    }

                                    // 检查必要的位置字段是否存在
                                    if (!agent_data.contains("pos_x") || !agent_data.contains("pos_y") || !agent_data.contains("pos_z")) {
                                        ROS_WARN("Agent %s missing position fields (pos_x/pos_y/pos_z), using defaults", each.c_str());
                                    }

                                    gazebo_msgs::ModelState tmp_model_state;
                                    tmp_model_state.model_name = each;
                                    // 更安全的字段访问：先检查字段存在且不为null
                                    if (agent_data.contains("pos_x") && !agent_data["pos_x"].is_null()) {
                                        tmp_model_state.pose.position.x = agent_data["pos_x"].get<double>();
                                    } else {
                                        tmp_model_state.pose.position.x = 0.0;
                                    }

                                    if (agent_data.contains("pos_y") && !agent_data["pos_y"].is_null()) {
                                        tmp_model_state.pose.position.y = agent_data["pos_y"].get<double>();
                                    } else {
                                        tmp_model_state.pose.position.y = 0.0;
                                    }

                                    if (agent_data.contains("pos_z") && !agent_data["pos_z"].is_null()) {
                                        tmp_model_state.pose.position.z = agent_data["pos_z"].get<double>();
                                    } else {
                                        tmp_model_state.pose.position.z = 0.0;
                                    }
                                    tmp_model_state.pose.orientation.w = 1.0;
                                    set_model_states_.publish(tmp_model_state);

                                    // 分布式LED状态可见性 - 每个节点都能看到其他节点的LED状态
                                    if (each != client_id_) {
                                        // 更安全的LED模式字段访问
                                        int other_led_mode = 0;
                                        if (agent_data.contains("led_mode") && !agent_data["led_mode"].is_null()) {
                                            other_led_mode = agent_data["led_mode"].get<int>();
                                        } else {
                                            ROS_WARN("Agent %s missing or null led_mode field, using default 0", each.c_str());
                                        }
                                    
                                    // 初始化其他节点LED状态跟踪
                                    if (other_nodes_led_status_.find(each) == other_nodes_led_status_.end()) {
                                        other_nodes_led_status_[each] = other_led_mode;
                                        ROS_INFO("Node %s initialized tracking for %s LED mode: %d", 
                                                mqtt_client_->client_id_.c_str(), each.c_str(), other_led_mode);
                                    } else if (other_nodes_led_status_[each] != other_led_mode) {
                                        int old_mode = other_nodes_led_status_[each];
                                        other_nodes_led_status_[each] = other_led_mode;
                                        ROS_INFO("Node %s observed %s LED change: %d -> %d", 
                                                mqtt_client_->client_id_.c_str(), each.c_str(), old_mode, other_led_mode);
                                        
                                        // 为其他节点创建独立的LED控制话题发布者
                                        publishOtherNodeLedStatus(each, other_led_mode);
                                    }
                                    
                                    // 主从同步逻辑（保持原有功能）
                                    if (!is_master_ && each == master_id_) {
                                        if (other_led_mode != last_led_mode_) {
                                            ROS_INFO("Slave %s syncing master %s LED mode: %d -> %d", 
                                                    mqtt_client_->client_id_.c_str(), each.c_str(), 
                                                    last_led_mode_, other_led_mode);
                                            std_msgs::Int32 msg;
                                            msg.data = other_led_mode;
                                            model_led_publishers_.publish(msg);
                                            last_led_mode_ = other_led_mode;
                                        }
                                    }
                                }
                                } catch (const std::exception& e) {
                                    ROS_WARN("Error processing agent %s: %s", each.c_str(), e.what());
                                }
                            }
                        }
                        
                        // 为每个模型创建独立的ROS发布器（添加严格的安全检查）
                        json agent_dict_copy2 = mqtt_client_->getAgentDictCopy();
                        for (auto it = agent_dict_copy2.begin(); it != agent_dict_copy2.end(); ++it) {
                            const std::string& model_name = it.key();
                            const json& state = it.value();

                            // 严格验证模型名称和状态数据
                            if (model_name.empty() || state.is_null() || !state.is_object()) {
                                ROS_WARN("Skipping model %s: empty name or invalid state", model_name.c_str());
                                continue;
                            }

                            // 关键修复：跳过看起来像属性名的键
                            if (model_name == "pos_x" || model_name == "pos_y" || model_name == "pos_z" ||
                                model_name == "q_x" || model_name == "q_y" || model_name == "q_z" || model_name == "q_w" ||
                                model_name == "led_mode") {
                                ROS_WARN("Skipping property key in model publisher loop: %s", model_name.c_str());
                                continue;
                            }

                            // 验证状态数据包含必要字段
                            if (!state.contains("pos_x") || !state.contains("pos_y") || !state.contains("pos_z")) {
                                ROS_WARN("Model %s missing required position fields, skipping publisher", model_name.c_str());
                                continue;
                            }

                            try {
                                std::string topic_name = "/" + model_name + "/model_states";

                                if (model_state_publishers_.find(model_name) == model_state_publishers_.end()) {
                                    model_state_publishers_[model_name] = nh.advertise<geometry_msgs::Pose>(topic_name, 10);
                                }

                                // 更安全的字段访问：先检查字段存在且不为null
                                geometry_msgs::Pose pose_msg;

                                if (state.contains("pos_x") && !state["pos_x"].is_null()) {
                                    pose_msg.position.x = state["pos_x"].get<double>();
                                } else {
                                    pose_msg.position.x = 0.0;
                                    ROS_WARN("Model %s: pos_x missing or null, using default 0.0", model_name.c_str());
                                }

                                if (state.contains("pos_y") && !state["pos_y"].is_null()) {
                                    pose_msg.position.y = state["pos_y"].get<double>();
                                } else {
                                    pose_msg.position.y = 0.0;
                                    ROS_WARN("Model %s: pos_y missing or null, using default 0.0", model_name.c_str());
                                }

                                if (state.contains("pos_z") && !state["pos_z"].is_null()) {
                                    pose_msg.position.z = state["pos_z"].get<double>();
                                } else {
                                    pose_msg.position.z = 0.0;
                                    ROS_WARN("Model %s: pos_z missing or null, using default 0.0", model_name.c_str());
                                }

                                pose_msg.orientation.w = 1.0;
                                model_state_publishers_[model_name].publish(pose_msg);
                            } catch (const std::exception& e) {
                                ROS_WARN("Error publishing model state for %s: %s", model_name.c_str(), e.what());
                            }
                        }
                        
                    } catch (const std::exception& e) {
                        ROS_WARN("Error in LED control loop: %s", e.what());
                    }
                }
                
                rate.sleep();
                ros::spinOnce();
                
            } catch (const std::exception& e) {
                ROS_ERROR("ROS Exception: %s", e.what());
            }
        }
    }
    
private:
    std::shared_ptr<MQTTClientThread> mqtt_client_;
    std::string client_id_;  // 基础客户端ID（不带_car后缀）
    std::string model_name_;
    std::vector<std::string> model_list_;
    std::map<std::string, ros::Publisher> model_state_publishers_;
    std::map<std::string, json> model_states_dict_;
    
    // LED状态管理
    int last_led_mode_;
    int led_mode_;

    // 位置变量
    double px_, py_, pz_;

    // 主从控制设置
    bool is_master_;
    std::string master_id_;
    
    // ROS发布者和订阅者
    ros::Publisher set_model_states_;
    ros::Publisher model_led_publishers_;
    ros::Subscriber model_states_sub_;
    ros::Subscriber led_mode_sub_;
    
    // LED状态同步
    double led_sync_timestamp_;
    std::map<std::string, int> other_nodes_led_status_;
    std::map<std::string, ros::Publisher> other_node_led_mode_publishers_;
    
    double getCurrentTime() {
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration<double>(duration).count();
    }
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "car_sim_cpp");
    
    try {
        // 获取主机名作为客户端ID（与Python版本保持一致）
        char hostname[256];
        gethostname(hostname, sizeof(hostname));
        std::string client_id = std::string(hostname);       // 基础客户端ID（用于模型名称和agent名称）
        std::string mqtt_client_id = client_id + "_car";     // MQTT客户端ID
        
        // MQTT配置
        std::string broker = "*************";
        int port = 1883;
        int keepalive = 60;
        
        // 启动MQTT客户端线程
        auto mqtt_client_instance = std::make_shared<MQTTClientThread>(broker, port, keepalive, mqtt_client_id);
        std::thread mqtt_thread([mqtt_client_instance]() {
            mqtt_client_instance->run();
        });
        
        mqtt_client_instance->setSubscribe();
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 启动ROS线程
        GazeboNodeThread ros_thread_instance(mqtt_client_instance, client_id);
        std::thread ros_thread([&ros_thread_instance]() {
            ros_thread_instance.run();
        });
        
        ROS_INFO("CarSim C++ started for model: %s", client_id.c_str());
        
        // 等待线程结束
        mqtt_thread.join();
        ros_thread.join();
        
    } catch (const std::exception& e) {
        ROS_ERROR("Error: %s", e.what());
        return 1;
    }
    
    return 0;
}
