<?xml version="1.0" ?>
<sdf version='1.5'>
  <model name='swarm_car'>
    <static>false</static>
    <link name='base_link'>
      <inertial>
        <pose>0.000194 -0 0.040331 0 -0 0</pose>
        <mass>4</mass>
        <inertia>
          <ixx>0.104673</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.104794</iyy>
          <iyz>0</iyz>
          <izz>0.104794</izz>
        </inertia>
      </inertial>
      <collision name='base_link_collision'>
        <pose>0 0 0.1 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.155</radius>
            <length>0.2</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name='base_link_visual'>
        <pose>0 0 0 0 -0 1.57</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://swarm_car/meshes/base_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Black</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='base_link_fixed_joint_lump__upper_link_visual_3'>
        <pose>0.005 0 0.136397 0 -0 3.14</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://swarm_car/meshes/upper_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Black</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
    </link>

    <joint name='lower_link_joint' type='prismatic'>
      <pose relative_to='base_link'>0 0 0.075 0 -0 3.14</pose>
      <parent>base_link</parent>
      <child>lower_link</child>
      <axis>
        <xyz>0 0 -1</xyz>
        <limit>
          <lower>0</lower>
          <upper>0.01</upper>
          <effort>0</effort>
          <velocity>0</velocity>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name='lower_link'>
      <pose relative_to='lower_link_joint'>0 0 0 0 -0 0</pose>
      <inertial>
        <pose>-2e-06 2e-06 -0.016933 0 -0 0</pose>
        <mass>0.113</mass>
        <inertia>
          <ixx>4.0739e-05</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>4.0739e-05</iyy>
          <iyz>0</iyz>
          <izz>5.4001e-05</izz>
        </inertia>
      </inertial>
      <visual name='lower_link_visual'>
        <pose>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://swarm_car/meshes/lower_link.STL</uri>
          </mesh>
        </geometry>
      </visual>
      <light name='lower_link_visual' type='point'>
        <pose>0 0 0 0 0 0</pose>
        <attenuation>
          <range>1.0</range>
          <linear>0.05</linear>
        </attenuation>
      </light>
    </link>
    <plugin name='lower_link_control' filename='libLedPlugin.so'>
      <enable>true</enable>
      <light>
        <id>lower_link/lower_link_visual</id>
        <block>
          <duration>0.1</duration>
          <interval>0</interval>
          <color>0 0 0 1</color>
        </block>
        <enable>true</enable>
      </light>
    </plugin>

    <joint name='light_joint' type='prismatic'>
      <pose relative_to='base_link'>0 0 0 0 -0 0</pose>
      <parent>base_link</parent>
      <child>light</child>
      <axis>
        <xyz>0 0 -1</xyz>
        <limit>
          <lower>0</lower>
          <upper>0</upper>
          <effort>0</effort>
          <velocity>0</velocity>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name='light'>
      <pose relative_to='light_joint'>0 0 0 0 -0 0</pose>
      <inertial>
        <pose>-2e-06 2e-06 -0.016933 0 -0 0</pose>
        <mass>0.113</mass>
        <inertia>
          <ixx>4.0739e-05</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>4.0739e-05</iyy>
          <iyz>0</iyz>
          <izz>5.4001e-05</izz>
        </inertia>
      </inertial>
      <visual name='light_visual'>
        <pose>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://swarm_car/meshes/light_link.STL</uri>
          </mesh>
        </geometry>
      </visual>
      <light name='light_visual' type='point'>
        <pose>0 0 0 0 0 0</pose>
        <attenuation>
          <!-- 光照范围 -->
          <range>0.10</range>
          <linear>0.01</linear>
          <!-- 光衰 -->
          <!-- <constant>0.01</constant>
          <linear>0.01</linear>   -->
        </attenuation>
        <diffuse>1 0.2 0.2 1</diffuse>
        <specular>1 1 1 1</specular>
      </light>
    </link>
    <plugin name='light_control' filename='libLedPlugin.so'>
      <enable>true</enable>
      <light>
        <id>light/light_visual</id>
        <block>
          <duration>0.1</duration>
          <interval>0</interval>
          <color>0.0 0.0 0.0 1.0</color>
        </block>
        <enable>true</enable>
      </light>
    </plugin>
    <plugin name="my_plugin" filename="libled_gazebo_plugin.so">
    </plugin>

    <frame name='upper_link' attached_to='upper_link_joint'/>

    <joint name='cam0_joint' type='fixed'>
      <pose relative_to='base_link'>0 0 0 0 0 0</pose>
      <parent>base_link</parent>
      <child>cam0</child>
      <axis>
        <xyz>0 0 -1</xyz>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <joint name='cam1_joint' type='fixed'>
      <pose relative_to='base_link'>0 0 0 0 0 0</pose>
      <parent>base_link</parent>
      <child>cam1</child>
      <axis>
        <xyz>0 0 -1</xyz>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <joint name='cam2_joint' type='fixed'>
      <pose relative_to='base_link'>0 0 0 0 0 0</pose>
      <parent>base_link</parent>
      <child>cam2</child>
      <axis>
        <xyz>0 0 -1</xyz>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <joint name='cam3_joint' type='fixed'>
      <pose relative_to='base_link'>0 0 0 0 0 0</pose>
      <parent>base_link</parent>
      <child>cam3</child>
      <axis>
        <xyz>0 0 -1</xyz>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
      </axis>
    </joint>
    <link name="cam0">
      <pose>-0.03 0 0.17 0 0 3.14</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <!-- <visual name="visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <box>
              <size>0.001 0.03 0.03</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Green</name>
          </script>
        </material>
      </visual> -->
      <sensor type="camera" name="camera_node">
        <update_rate>20.0</update_rate>
        <camera name="head">
          <horizontal_fov>1.3962634</horizontal_fov>
          <image>
            <width>640</width>
            <height>360</height>
            <format>R8G8B8</format>
          </image>
          <clip>
            <near>0.02</near>
            <far>300</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.007</stddev>
          </noise>
        </camera>
        <plugin name="gazebo_camera" filename="libgazebo_ros_camera.so">
          <alwaysOn>true</alwaysOn>
          <updateRate>0.0</updateRate>
          <cameraName>/cam0</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>camera0</frameName>
          <hackBaseline>0.07</hackBaseline>
          <distortionK1>-0.28340811</distortionK1>
          <distortionK2>0.07395907</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>  
    </link>

    <link name="cam1">
      <pose>-0 -0.03 0.17 0 0 -1.57</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <!-- <visual name="visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <box>
              <size>0.001 0.03 0.03</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Blue</name>
          </script>
        </material>
      </visual> -->
      <sensor type="camera" name="camera_node">
        <update_rate>20.0</update_rate>
        <camera name="head">
          <horizontal_fov>1.3962634</horizontal_fov>
          <image>
            <width>640</width>
            <height>360</height>
            <format>R8G8B8</format>
          </image>
          <clip>
            <near>0.02</near>
            <far>300</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.007</stddev>
          </noise>
        </camera>
        <plugin name="gazebo_camera" filename="libgazebo_ros_camera.so">
          <alwaysOn>true</alwaysOn>
          <updateRate>0.0</updateRate>
          <cameraName>/cam1</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>camera0</frameName>
          <hackBaseline>0.07</hackBaseline>
          <distortionK1>-0.28340811</distortionK1>
          <distortionK2>0.07395907</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>  
    </link>

    <link name="cam2">
      <pose>0.03 0 0.17 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <!-- <visual name="visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <box>
              <size>0.001 0.03 0.03</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Red</name>
          </script>
        </material>
      </visual> -->
      <sensor type="camera" name="camera_node">
        <update_rate>20.0</update_rate>
        <camera name="head">
          <horizontal_fov>1.3962634</horizontal_fov>
          <image>
            <width>640</width>
            <height>360</height>
            <format>R8G8B8</format>
          </image>
          <clip>
            <near>0.02</near>
            <far>300</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.007</stddev>
          </noise>
        </camera>
        <plugin name="gazebo_camera" filename="libgazebo_ros_camera.so">
          <alwaysOn>true</alwaysOn>
          <updateRate>0.0</updateRate>
          <cameraName>/cam2</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>camera0</frameName>
          <hackBaseline>0.07</hackBaseline>
          <distortionK1>-0.28340811</distortionK1>
          <distortionK2>0.07395907</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>  
    </link>

    <link name="cam3">
      <pose>0 0.03 0.17 0 0 1.57</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.01</mass>
        <inertia>
          <ixx>4.15e-6</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>2.407e-6</iyy>
          <iyz>0</iyz>
          <izz>2.407e-6</izz>
        </inertia>
      </inertial>
      <!-- <visual name="visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <box>
              <size>0.001 0.03 0.03</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Yellow</name>
          </script>
        </material>
      </visual> -->
      <sensor type="camera" name="camera_node">
        <update_rate>20.0</update_rate>
        <camera name="head">
          <horizontal_fov>1.3962634</horizontal_fov>
          <image>
            <width>640</width>
            <height>360</height>
            <format>R8G8B8</format>
          </image>
          <clip>
            <near>0.02</near>
            <far>300</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.007</stddev>
          </noise>
        </camera>
        <plugin name="gazebo_camera" filename="libgazebo_ros_camera.so">
          <alwaysOn>true</alwaysOn>
          <updateRate>0.0</updateRate>
          <cameraName>/cam3</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>camera0</frameName>
          <hackBaseline>0.07</hackBaseline>
          <distortionK1>-0.28340811</distortionK1>
          <distortionK2>0.07395907</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>  
    </link> 
    <plugin name="p3d_base_controller" filename="libgazebo_ros_p3d.so">
        <alwaysOn>true</alwaysOn>
        <updateRate>50.0</updateRate>
        <bodyName>base_link</bodyName>
        <topicName>pose_gt</topicName>
        <gaussianNoise>0.0</gaussianNoise>
        <frameName>world</frameName>
        <xyzOffset>0 0 0</xyzOffset>
        <rpyOffset>0 0 0</rpyOffset>
    </plugin>
  </model>
</sdf>
