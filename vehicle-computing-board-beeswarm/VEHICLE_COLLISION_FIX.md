# 车辆重叠问题修复说明

## 问题描述

分布式小车系统中存在车辆重叠问题，多个车辆可以占据同一位置而不会相互推开。

## 根本原因

车辆模型被设置为静态物体（`<static>true</static>`），导致：
- 车辆不参与Gazebo的物理碰撞检测
- 多个车辆可以重叠在同一位置
- 缺乏真实的物理交互

## 解决方案：方案A（混合方案）

### 修改内容

1. **车辆SDF模型修改**：
   ```xml
   <!-- 修改前 -->
   <static>true</static>
   
   <!-- 修改后 -->
   <static>false</static>
   ```

2. **安全距离参数调整**：
   ```python
   # 修改前
   'safe_distance': 1.0,      # 最小安全距离(米)
   'retreat_speed': 0.3,      # 后退速度
   
   # 修改后  
   'safe_distance': 1.5,      # 增加到1.5米防止重叠
   'retreat_speed': 0.4,      # 稍微增加避障速度
   ```

### 修改的文件

1. `vehicle-computing-board-beeswarm/vswarm_sim_ws/src/vswarm_sim/sdf/swarm_car/swarm_car.sdf`
2. `vehicle-computing-board-beeswarm/vswarm_sim_ws/src/vswarm_sim/sdf/swarm_car/swarm_car_camera_nopub.sdf`
3. `ros/swarm_experiment/core/config.py`
4. `ros/V1/V6simple_2d_swarm_experiment.py`
5. `ros/V1/V7simple_2d_swarm_experiment.py`

## 预期效果

### ✅ 优点
- **现有代码完全兼容**：所有控制逻辑保持不变
- **解决重叠问题**：车辆现在会有物理碰撞
- **更真实的行为**：车辆会相互推开而不是重叠
- **保持控制精度**：仍然使用位置控制，运动精确

### ⚠️ 注意事项
- 车辆现在有质量和惯性，可能会有轻微的物理反弹
- 在狭窄空间中的运动可能会受到物理约束
- 需要测试确保控制性能仍然满足要求

## 测试方法

### 1. 基本功能测试

```bash
# 启动仿真环境
cd ~/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash
roslaunch vswarm_sim start_car_cpp.launch

# 在另一个终端运行测试脚本
cd ~/BeeSwarm/Code/vehicle-computing-board-beeswarm
python3 test_vehicle_collision.py
```

### 2. 多车辆碰撞测试

如果有多个车辆：
1. 启动多个车辆实例
2. 尝试将它们移动到相同位置
3. 观察是否会相互推开

### 3. 控制性能测试

```bash
# 测试现有的控制脚本是否正常工作
cd ~/BeeSwarm/Code/ros/V1
python3 V7simple_2d_swarm_experiment.py
```

## 验证清单

- [ ] 车辆能正常启动和移动
- [ ] 车辆不会重叠在同一位置
- [ ] 现有的避障算法仍然有效
- [ ] 车辆控制精度满足要求
- [ ] 多车辆系统中车辆会相互避让

## 回滚方案

如果修改导致问题，可以快速回滚：

```bash
# 恢复静态车辆模型
sed -i 's/<static>false<\/static>/<static>true<\/static>/g' \
  vehicle-computing-board-beeswarm/vswarm_sim_ws/src/vswarm_sim/sdf/swarm_car/*.sdf

# 恢复原始安全距离参数
# 手动编辑配置文件，将 safe_distance 改回 1.0，retreat_speed 改回 0.3
```

## 进一步优化建议

如果方案A效果良好，可以考虑：

1. **调整车辆物理参数**：
   - 质量、惯性矩阵
   - 摩擦系数
   - 阻尼参数

2. **优化碰撞几何体**：
   - 调整碰撞体大小
   - 使用更精确的碰撞形状

3. **增强避障算法**：
   - 添加预测性避障
   - 实现协调避障策略

## 技术细节

### 车辆碰撞几何体

当前车辆使用圆柱体碰撞几何：
- `swarm_car.sdf`: 半径 0.155m，高度 0.2m
- `swarm_car_camera_nopub.sdf`: 半径 0.2m，高度 0.2m

### 物理引擎设置

Gazebo使用ODE物理引擎，相关参数在 `lab_106.world` 中配置。

### 控制方式

仍然使用 `set_model_state` 服务直接设置车辆位置，但现在车辆是动态物体，会参与碰撞检测。

---

**修改完成时间**: 2025-08-09  
**修改类型**: 车辆重叠问题修复（方案A）  
**影响范围**: 车辆物理行为，不影响控制接口
